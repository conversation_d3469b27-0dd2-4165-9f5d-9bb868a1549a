"""
API路由
"""
from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_current_user
from datetime import datetime

from app.api import api_bp
from app import db
from app.models import SystemConfig, SystemNotification, FileInfo, FileShare
from app.utils.security import get_client_ip, get_user_agent
from app.utils.logger import log_access, get_system_activity_stats
from sqlalchemy import func

@api_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    }), 200

@api_bp.route('/system/info', methods=['GET'])
def get_system_info():
    """获取系统基本信息"""
    try:
        # 获取公开配置
        configs = SystemConfig.query.filter_by(is_public=True).all()
        system_config = {}
        for config in configs:
            system_config[config.config_key] = config.get_value()
        
        # 获取活跃通知
        notifications = SystemNotification.query.filter_by(
            is_active=True,
            show_on_homepage=True
        ).filter(
            SystemNotification.is_global == True
        ).order_by(SystemNotification.priority.desc()).all()
        
        active_notifications = []
        for notification in notifications:
            if notification.is_visible():
                active_notifications.append(notification.to_dict())
        
        # 获取系统统计
        stats = get_system_activity_stats(days=1)  # 今日统计
        
        return jsonify({
            'system_name': system_config.get('system_name', '企业文件共享系统'),
            'system_version': system_config.get('system_version', '1.0.0'),
            'notifications': active_notifications,
            'stats': {
                'total_visits_today': stats.get('total_visits', 0),
                'download_count_today': stats.get('download_count', 0),
                'search_count_today': stats.get('search_count', 0),
                'active_users_today': stats.get('active_users', 0)
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"获取系统信息错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@api_bp.route('/system/stats', methods=['GET'])
@jwt_required()
def get_system_stats():
    """获取系统统计信息"""
    try:
        current_user = get_current_user()
        days = request.args.get('days', 30, type=int)
        
        # 获取系统活动统计
        stats = get_system_activity_stats(days=days)
        
        # 获取文件统计
        total_files = FileInfo.query.filter_by(is_available=True, is_deleted=False).count()
        total_size = db.session.query(func.sum(FileInfo.file_size)).filter_by(
            is_available=True, is_deleted=False
        ).scalar() or 0
        
        # 获取共享统计
        total_shares = FileShare.query.filter_by(is_active=True).count()
        
        # 获取用户统计
        from app.models import User
        total_users = User.query.filter_by(is_active=True).count()
        
        return jsonify({
            'period_days': days,
            'file_stats': {
                'total_files': total_files,
                'total_size': total_size,
                'total_shares': total_shares
            },
            'user_stats': {
                'total_users': total_users,
                'active_users': stats.get('active_users', 0)
            },
            'activity_stats': stats
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"获取系统统计错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@api_bp.route('/notifications', methods=['GET'])
@jwt_required()
def get_notifications():
    """获取用户通知"""
    try:
        current_user = get_current_user()
        
        # 获取全局通知
        global_notifications = SystemNotification.query.filter_by(
            is_active=True,
            is_global=True
        ).order_by(SystemNotification.priority.desc()).all()
        
        # 获取针对用户的通知
        user_notifications = SystemNotification.query.filter(
            SystemNotification.is_active == True,
            SystemNotification.is_global == False,
            SystemNotification.target_users.contains(str(current_user.id))
        ).order_by(SystemNotification.priority.desc()).all()
        
        # 获取针对用户组的通知
        group_notifications = SystemNotification.query.filter(
            SystemNotification.is_active == True,
            SystemNotification.is_global == False,
            SystemNotification.target_groups.contains(str(current_user.group_id))
        ).order_by(SystemNotification.priority.desc()).all()
        
        all_notifications = []
        
        # 合并所有通知
        for notification in global_notifications + user_notifications + group_notifications:
            if notification.is_visible():
                all_notifications.append(notification.to_dict())
        
        # 按优先级排序
        all_notifications.sort(key=lambda x: x['priority'], reverse=True)
        
        return jsonify({'notifications': all_notifications}), 200
        
    except Exception as e:
        current_app.logger.error(f"获取通知错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@api_bp.route('/notifications/<int:notification_id>/view', methods=['POST'])
@jwt_required()
def mark_notification_viewed(notification_id):
    """标记通知为已查看"""
    try:
        notification = SystemNotification.query.get(notification_id)
        if notification:
            notification.view_count += 1
            db.session.commit()
        
        return jsonify({'message': '标记成功'}), 200
        
    except Exception as e:
        current_app.logger.error(f"标记通知查看错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@api_bp.route('/notifications/<int:notification_id>/click', methods=['POST'])
@jwt_required()
def mark_notification_clicked(notification_id):
    """标记通知为已点击"""
    try:
        notification = SystemNotification.query.get(notification_id)
        if notification:
            notification.click_count += 1
            db.session.commit()
        
        return jsonify({'message': '标记成功'}), 200
        
    except Exception as e:
        current_app.logger.error(f"标记通知点击错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@api_bp.route('/user/profile', methods=['GET'])
@jwt_required()
def get_user_profile():
    """获取用户资料"""
    try:
        current_user = get_current_user()
        
        # 获取用户活动统计
        from app.utils.logger import get_user_activity_stats
        activity_stats = get_user_activity_stats(current_user.id, days=30)
        
        profile = current_user.to_dict()
        profile['activity_stats'] = activity_stats
        
        return jsonify({'profile': profile}), 200
        
    except Exception as e:
        current_app.logger.error(f"获取用户资料错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@api_bp.route('/user/profile', methods=['PUT'])
@jwt_required()
def update_user_profile():
    """更新用户资料"""
    try:
        current_user = get_current_user()
        data = request.get_json()
        
        # 只允许更新部分字段
        allowed_fields = ['real_name', 'phone', 'department']
        updated_fields = []
        
        for field in allowed_fields:
            if field in data:
                old_value = getattr(current_user, field)
                new_value = data[field]
                if old_value != new_value:
                    setattr(current_user, field, new_value)
                    updated_fields.append(f"{field}: {old_value} -> {new_value}")
        
        # 更新密码
        if data.get('new_password'):
            if not data.get('current_password'):
                return jsonify({'error': '请提供当前密码'}), 400
            
            if not current_user.check_password(data['current_password']):
                return jsonify({'error': '当前密码错误'}), 400
            
            if len(data['new_password']) < 6:
                return jsonify({'error': '新密码长度不能少于6个字符'}), 400
            
            current_user.set_password(data['new_password'])
            updated_fields.append('密码已更新')
        
        if updated_fields:
            db.session.commit()
            
            # 记录操作日志
            from app.utils.logger import log_operation
            log_operation(
                current_user.id, 'update_profile', 'user_profile',
                f'更新个人资料: {", ".join(updated_fields)}',
                ip_address=get_client_ip(),
                user_agent=get_user_agent(),
                result='success'
            )
        
        return jsonify({
            'message': '资料更新成功' if updated_fields else '没有变更',
            'profile': current_user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"更新用户资料错误: {str(e)}")
        db.session.rollback()
        return jsonify({'error': '服务器内部错误'}), 500
