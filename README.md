# 企业文件共享系统

一个功能强大的企业级文件共享系统，支持内外网访问控制、双搜索引擎、实时监控、加密下载等功能。

## 主要功能

### 核心功能
- **权限管理**：内网/外网访问控制，用户权限分级（只读、修改、删除等）
- **双搜索引擎**：Everything-like快速搜索 + 图像识别搜索
- **文件管理**：缩略图支持、多格式支持、批量操作
- **下载控制**：加密下载、限流、权限控制
- **监控日志**：用户行为记录、实时监控、统计分析
- **用户管理**：分组管理、登录控制、远程管理

### 技术特性
- **前后端分离**：管理员和用户界面完全分离
- **安全性**：JWT认证、密码加密、限流保护
- **可扩展性**：模块化设计、配置化管理
- **中文支持**：完整的中文界面和文件名支持
- **Windows部署**：无需Docker，直接在Windows上部署

## 系统要求

### 软件要求
- Python 3.8+
- MySQL 5.7+
- Windows 10/11 或 Windows Server

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 硬盘: 根据文件存储需求

## 快速安装

### 1. 下载源码
```bash
git clone <repository-url>
cd Net
```

### 2. 运行安装脚本
```bash
python install.py
```

安装脚本会自动：
- 检查Python版本
- 安装依赖包
- 检查MySQL连接
- 创建数据库
- 初始化系统数据
- 创建必要目录

### 3. 启动系统
```bash
python run.py
```

## 手动安装

如果自动安装失败，可以手动安装：

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
确保MySQL服务运行，创建数据库：
```sql
CREATE DATABASE file_sharing_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 修改配置
编辑 `config.py` 文件，修改数据库连接信息：
```python
MYSQL_USER = 'root'
MYSQL_PASSWORD = '123456'
MYSQL_DATABASE = 'file_sharing_system'
```

### 4. 初始化数据库
```bash
python init_db.py
```

### 5. 启动系统
```bash
python run.py
```

## 默认账户

### 管理员账户
- 用户名: `admin`
- 密码: `admin123`
- 访问地址: http://localhost:5000/admin

### 测试用户账户
- 用户名: `testuser`
- 密码: `test123`
- 访问地址: http://localhost:5000/

## 系统配置

### 基础配置
系统配置通过管理员界面进行，主要配置项包括：

- **系统名称和版本**
- **文件上传限制**
- **搜索引擎开关**
- **下载加密设置**
- **安全策略**
- **外网访问控制**

### 用户组管理
支持创建多个用户组，每个组可以设置不同的权限：

- **基础权限**：读取、写入、删除、下载、上传、分享
- **下载限制**：最大文件大小、数量限制、每日限额
- **访问控制**：内网/外网访问权限

### 文件共享配置
可以配置多个共享文件夹，每个共享支持：

- **访问控制**：内网/外网访问
- **权限设置**：读写删除权限
- **下载限制**：大小和数量限制
- **加密设置**：下载加密阈值
- **监控设置**：敏感词过滤

## 功能说明

### 搜索功能
系统提供两种搜索引擎：

1. **文本搜索**：类似Everything的快速文件名搜索
2. **图像搜索**：基于OpenCV的图像识别搜索

### 下载功能
- **单文件下载**：直接下载单个文件
- **批量下载**：选择多个文件打包下载
- **文件夹下载**：整个文件夹打包下载
- **加密下载**：超过阈值后自动加密压缩包

### 监控功能
- **访问日志**：记录所有用户访问
- **操作日志**：记录文件操作行为
- **下载日志**：记录下载详情
- **搜索日志**：记录搜索行为
- **实时统计**：在线用户、下载量等

### 通知功能
- **系统通知**：支持滚动字幕显示
- **用户通知**：针对特定用户或组
- **图片通知**：支持图片附件
- **优先级**：支持通知优先级排序

## API文档

系统提供完整的REST API，主要接口包括：

### 认证接口
- `POST /auth/login` - 用户登录
- `POST /auth/admin/login` - 管理员登录
- `POST /auth/logout` - 登出
- `POST /auth/refresh` - 刷新令牌

### 管理员接口
- `GET /admin/users` - 获取用户列表
- `POST /admin/users` - 创建用户
- `PUT /admin/users/{id}` - 更新用户
- `DELETE /admin/users/{id}` - 删除用户
- `GET /admin/groups` - 获取用户组列表
- `POST /admin/groups` - 创建用户组

### 系统接口
- `GET /api/health` - 健康检查
- `GET /api/system/info` - 系统信息
- `GET /api/system/stats` - 系统统计
- `GET /api/notifications` - 获取通知

## 开发说明

### 项目结构
```
Net/
├── app/                    # 应用主目录
│   ├── __init__.py        # 应用工厂
│   ├── models/            # 数据模型
│   ├── auth/              # 认证模块
│   ├── admin/             # 管理员模块
│   ├── api/               # API模块
│   └── utils/             # 工具函数
├── config.py              # 配置文件
├── requirements.txt       # 依赖列表
├── install.py            # 安装脚本
├── init_db.py            # 数据库初始化
├── run.py                # 启动文件
└── README.md             # 说明文档
```

### 数据库设计
系统使用MySQL数据库，主要表结构：

- **users** - 用户表
- **user_groups** - 用户组表
- **file_info** - 文件信息表
- **file_shares** - 文件共享表
- **access_logs** - 访问日志表
- **operation_logs** - 操作日志表
- **download_logs** - 下载日志表
- **system_configs** - 系统配置表
- **system_notifications** - 系统通知表

## 常见问题

### 1. 数据库连接失败
检查MySQL服务是否启动，用户名密码是否正确。

### 2. 依赖安装失败
使用国内镜像源：
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. 文件上传失败
检查上传目录权限，确保应用有写入权限。

### 4. 搜索功能异常
检查搜索索引目录是否存在，重新建立索引。

## 技术支持

如有问题，请检查：
1. 系统日志文件 `logs/system.log`
2. 数据库连接配置
3. 文件目录权限
4. 防火墙设置

## 许可证

本项目为企业内部使用，请勿用于商业用途。
