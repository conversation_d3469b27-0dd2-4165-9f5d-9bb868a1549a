"""
日志相关数据模型
"""
from datetime import datetime
from app import db

class AccessLog(db.Model):
    """访问日志模型"""
    __tablename__ = 'access_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON>ey('users.id'), nullable=True)
    
    # 访问信息
    ip_address = db.Column(db.String(45), nullable=False, comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    request_method = db.Column(db.String(10), comment='请求方法')
    request_url = db.Column(db.Text, comment='请求URL')
    request_params = db.Column(db.Text, comment='请求参数')
    
    # 响应信息
    response_status = db.Column(db.Integer, comment='响应状态码')
    response_size = db.Column(db.Integer, comment='响应大小')
    response_time = db.Column(db.Float, comment='响应时间(秒)')
    
    # 地理位置信息
    country = db.Column(db.String(50), comment='国家')
    region = db.Column(db.String(50), comment='地区')
    city = db.Column(db.String(50), comment='城市')
    
    # 访问类型
    access_type = db.Column(db.String(20), comment='访问类型(internal/external)')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        username = self.user.username if self.user else 'Anonymous'
        return f'<AccessLog {username}@{self.ip_address}>'

class OperationLog(db.Model):
    """操作日志模型"""
    __tablename__ = 'operation_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # 操作信息
    operation_type = db.Column(db.String(50), nullable=False, comment='操作类型')
    operation_target = db.Column(db.String(100), comment='操作目标')
    operation_detail = db.Column(db.Text, comment='操作详情')
    
    # 文件相关
    file_path = db.Column(db.Text, comment='文件路径')
    file_name = db.Column(db.String(255), comment='文件名')
    file_size = db.Column(db.BigInteger, comment='文件大小')
    
    # 结果信息
    operation_result = db.Column(db.String(20), comment='操作结果(success/failed)')
    error_message = db.Column(db.Text, comment='错误信息')
    
    # 网络信息
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    
    # 风险等级
    risk_level = db.Column(db.String(20), default='low', comment='风险等级(low/medium/high)')
    is_sensitive = db.Column(db.Boolean, default=False, comment='是否敏感操作')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        return f'<OperationLog {self.user.username}:{self.operation_type}>'

class DownloadLog(db.Model):
    """下载日志模型"""
    __tablename__ = 'download_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    file_id = db.Column(db.Integer, db.ForeignKey('file_info.id'), nullable=True)
    
    # 下载信息
    download_type = db.Column(db.String(20), comment='下载类型(single/batch/folder)')
    file_count = db.Column(db.Integer, default=1, comment='文件数量')
    total_size = db.Column(db.BigInteger, comment='总大小')
    
    # 文件信息
    file_names = db.Column(db.Text, comment='文件名列表')
    file_paths = db.Column(db.Text, comment='文件路径列表')
    
    # 下载状态
    download_status = db.Column(db.String(20), comment='下载状态(started/completed/failed/cancelled)')
    download_progress = db.Column(db.Float, default=0.0, comment='下载进度')
    
    # 加密信息
    is_encrypted = db.Column(db.Boolean, default=False, comment='是否加密')
    encryption_password = db.Column(db.String(255), comment='加密密码')
    download_count = db.Column(db.Integer, default=0, comment='该文件下载次数')
    
    # 网络信息
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    
    # 时间信息
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime, comment='完成时间')
    duration = db.Column(db.Float, comment='下载耗时(秒)')
    
    # 关系
    file = db.relationship('FileInfo', backref='download_logs')
    
    def calculate_duration(self):
        """计算下载耗时"""
        if self.completed_at and self.started_at:
            self.duration = (self.completed_at - self.started_at).total_seconds()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'file_id': self.file_id,
            'download_type': self.download_type,
            'file_count': self.file_count,
            'total_size': self.total_size,
            'file_names': self.file_names,
            'download_status': self.download_status,
            'download_progress': self.download_progress,
            'is_encrypted': self.is_encrypted,
            'ip_address': self.ip_address,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration': self.duration
        }
    
    def __repr__(self):
        return f'<DownloadLog {self.user.username}:{self.download_type}>'

class SearchLog(db.Model):
    """搜索日志模型"""
    __tablename__ = 'search_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # 搜索信息
    search_query = db.Column(db.Text, nullable=False, comment='搜索查询')
    search_type = db.Column(db.String(20), comment='搜索类型(text/image)')
    search_engine = db.Column(db.String(20), comment='搜索引擎(everything/image)')
    
    # 搜索结果
    result_count = db.Column(db.Integer, comment='结果数量')
    search_time = db.Column(db.Float, comment='搜索耗时(秒)')
    
    # 过滤条件
    file_types = db.Column(db.String(200), comment='文件类型过滤')
    size_range = db.Column(db.String(50), comment='大小范围')
    date_range = db.Column(db.String(50), comment='日期范围')
    
    # 网络信息
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    
    # 敏感性检查
    is_sensitive = db.Column(db.Boolean, default=False, comment='是否敏感搜索')
    blocked_keywords = db.Column(db.Text, comment='被阻止的关键词')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        username = self.user.username if self.user else 'Anonymous'
        return f'<SearchLog {username}:{self.search_query[:50]}>'
