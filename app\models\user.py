"""
用户相关数据模型
"""
from datetime import datetime, timed<PERSON>ta
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class UserGroup(db.Model):
    """用户组模型"""
    __tablename__ = 'user_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False, comment='组名')
    description = db.Column(db.Text, comment='组描述')
    
    # 权限设置
    can_read = db.Column(db.<PERSON>, default=True, comment='读取权限')
    can_write = db.Column(db.<PERSON>, default=False, comment='写入权限')
    can_delete = db.Column(db.<PERSON>, default=False, comment='删除权限')
    can_download = db.Column(db.<PERSON>, default=True, comment='下载权限')
    can_upload = db.Column(db.<PERSON>, default=False, comment='上传权限')
    can_share = db.Column(db.<PERSON>, default=False, comment='分享权限')
    
    # 限制设置
    max_download_size = db.Column(db.BigInteger, default=100*1024*1024, comment='最大下载大小(字节)')
    max_download_count = db.Column(db.Integer, default=50, comment='最大下载数量')
    daily_download_limit = db.Column(db.Integer, default=1000, comment='每日下载限制')
    
    # 访问控制
    internal_access = db.Column(db.Boolean, default=True, comment='内网访问')
    external_access = db.Column(db.Boolean, default=False, comment='外网访问')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    users = db.relationship('User', backref='group', lazy='dynamic')
    
    def __repr__(self):
        return f'<UserGroup {self.name}>'

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, comment='用户名')
    email = db.Column(db.String(120), unique=True, nullable=True, comment='邮箱')
    password_hash = db.Column(db.String(255), nullable=False, comment='密码哈希')
    
    # 用户信息
    real_name = db.Column(db.String(100), comment='真实姓名')
    phone = db.Column(db.String(20), comment='电话号码')
    department = db.Column(db.String(100), comment='部门')
    
    # 状态信息
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    is_admin = db.Column(db.Boolean, default=False, comment='是否管理员')
    is_banned = db.Column(db.Boolean, default=False, comment='是否被禁用')
    ban_until = db.Column(db.DateTime, comment='禁用到期时间')
    
    # 登录信息
    last_login = db.Column(db.DateTime, comment='最后登录时间')
    login_attempts = db.Column(db.Integer, default=0, comment='登录尝试次数')
    last_attempt_time = db.Column(db.DateTime, comment='最后尝试时间')
    
    # 统计信息
    total_downloads = db.Column(db.Integer, default=0, comment='总下载次数')
    total_uploads = db.Column(db.Integer, default=0, comment='总上传次数')
    total_download_size = db.Column(db.BigInteger, default=0, comment='总下载大小')
    
    # 外键
    group_id = db.Column(db.Integer, db.ForeignKey('user_groups.id'), nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    sessions = db.relationship('UserSession', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    access_logs = db.relationship('AccessLog', backref='user', lazy='dynamic')
    operation_logs = db.relationship('OperationLog', backref='user', lazy='dynamic')
    download_logs = db.relationship('DownloadLog', backref='user', lazy='dynamic')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """检查密码"""
        return check_password_hash(self.password_hash, password)
    
    def is_banned_now(self):
        """检查当前是否被禁用"""
        if not self.is_banned:
            return False
        if self.ban_until and self.ban_until < datetime.utcnow():
            # 禁用期已过，自动解除禁用
            self.is_banned = False
            self.ban_until = None
            db.session.commit()
            return False
        return True
    
    def can_login(self):
        """检查是否可以登录"""
        return self.is_active and not self.is_banned_now()
    
    def increment_login_attempt(self):
        """增加登录尝试次数"""
        self.login_attempts += 1
        self.last_attempt_time = datetime.utcnow()
        
        # 如果尝试次数过多，临时禁用
        from flask import current_app
        max_attempts = current_app.config.get('MAX_LOGIN_ATTEMPTS', 5)
        timeout = current_app.config.get('LOGIN_ATTEMPT_TIMEOUT', 300)
        
        if self.login_attempts >= max_attempts:
            self.is_banned = True
            self.ban_until = datetime.utcnow() + timedelta(seconds=timeout)
    
    def reset_login_attempts(self):
        """重置登录尝试次数"""
        self.login_attempts = 0
        self.last_attempt_time = None
        self.last_login = datetime.utcnow()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'phone': self.phone,
            'department': self.department,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'is_banned': self.is_banned_now(),
            'group_id': self.group_id,
            'group_name': self.group.name if self.group else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'total_downloads': self.total_downloads,
            'total_uploads': self.total_uploads,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<User {self.username}>'

class UserSession(db.Model):
    """用户会话模型"""
    __tablename__ = 'user_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    session_token = db.Column(db.String(255), unique=True, nullable=False, comment='会话令牌')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    is_active = db.Column(db.Boolean, default=True, comment='是否活跃')
    expires_at = db.Column(db.DateTime, nullable=False, comment='过期时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_activity = db.Column(db.DateTime, default=datetime.utcnow)
    
    def is_expired(self):
        """检查是否过期"""
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self):
        """检查会话是否有效"""
        return self.is_active and not self.is_expired()
    
    def __repr__(self):
        return f'<UserSession {self.user.username}>'
