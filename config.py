"""
系统配置文件
"""
import os
from datetime import timedelta

class Config:
    """基础配置"""
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '123456'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'file_sharing_system'
    
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': False
    }
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-string'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # 文件存储配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'
    THUMBNAIL_FOLDER = os.environ.get('THUMBNAIL_FOLDER') or 'thumbnails'
    TEMP_FOLDER = os.environ.get('TEMP_FOLDER') or 'temp'
    
    # 文件大小限制 (MB)
    MAX_CONTENT_LENGTH = 1024 * 1024 * 1024  # 1GB
    MAX_BATCH_DOWNLOAD_SIZE = 500  # 500MB
    MAX_BATCH_DOWNLOAD_COUNT = 100  # 100个文件
    
    # 支持的文件格式
    SUPPORTED_IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.psd', '.ai', '.eps'}
    SUPPORTED_VIDEO_FORMATS = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'}
    SUPPORTED_DOCUMENT_FORMATS = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}
    
    # 缩略图配置
    THUMBNAIL_SIZES = {
        'small': (150, 150),
        'medium': (300, 300),
        'large': (600, 600),
        'xlarge': (1200, 1200)
    }
    
    # 搜索引擎配置
    SEARCH_INDEX_PATH = os.environ.get('SEARCH_INDEX_PATH') or 'search_index'
    IMAGE_FEATURES_PATH = os.environ.get('IMAGE_FEATURES_PATH') or 'image_features'
    
    # 系统监控配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'logs/system.log'
    
    # 安全配置
    BCRYPT_LOG_ROUNDS = 12
    PASSWORD_MIN_LENGTH = 8
    MAX_LOGIN_ATTEMPTS = 5
    LOGIN_ATTEMPT_TIMEOUT = 300  # 5分钟
    
    # 下载加密配置
    ENCRYPTION_THRESHOLD = 3  # 下载3次后开始加密
    ENCRYPTION_PASSWORD_LENGTH = 12
    
    # 限流配置
    RATE_LIMIT_ENABLED = True
    RATE_LIMIT_REQUESTS_PER_MINUTE = 60
    RATE_LIMIT_DOWNLOADS_PER_HOUR = 100
    
    # 外网访问配置
    EXTERNAL_ACCESS_ENABLED = False
    EXTERNAL_ACCESS_PORT = 8080
    INTERNAL_ACCESS_PORT = 5000

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': True  # 开发环境显示SQL
    }

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    
class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    MYSQL_DATABASE = 'file_sharing_system_test'
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{Config.MYSQL_USER}:{Config.MYSQL_PASSWORD}@{Config.MYSQL_HOST}:{Config.MYSQL_PORT}/{MYSQL_DATABASE}_test?charset=utf8mb4'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
