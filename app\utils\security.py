"""
安全工具函数
"""
import hashlib
import secrets
import string
from datetime import datetime, timedelta
from flask import request, current_app
from typing import Optional, Dict, Any
import json
import os

# Redis连接（用于限流）
try:
    import redis
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
except ImportError:
    redis_client = None
except:
    redis_client = None

def get_client_ip() -> str:
    """获取客户端IP地址"""
    # 检查代理头
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr or '127.0.0.1'

def get_user_agent() -> str:
    """获取用户代理字符串"""
    return request.headers.get('User-Agent', 'Unknown')

def generate_random_password(length: int = 12) -> str:
    """生成随机密码"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    return password

def generate_file_hash(file_path: str) -> Optional[str]:
    """生成文件哈希值"""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return None

def generate_encryption_password(length: int = 12) -> str:
    """生成加密密码"""
    # 确保密码包含大小写字母、数字和特殊字符
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*"
    
    # 至少包含每种类型的一个字符
    password = [
        secrets.choice(lowercase),
        secrets.choice(uppercase),
        secrets.choice(digits),
        secrets.choice(special)
    ]
    
    # 填充剩余长度
    all_chars = lowercase + uppercase + digits + special
    for _ in range(length - 4):
        password.append(secrets.choice(all_chars))
    
    # 打乱顺序
    secrets.SystemRandom().shuffle(password)
    return ''.join(password)

def check_rate_limit(identifier: str, action: str, limit: int = None, window: int = None) -> bool:
    """检查限流"""
    if not redis_client:
        return True  # 如果Redis不可用，跳过限流检查
    
    # 默认限制配置
    default_limits = {
        'login': {'limit': 5, 'window': 300},  # 5次/5分钟
        'admin_login': {'limit': 3, 'window': 300},  # 3次/5分钟
        'download': {'limit': 100, 'window': 3600},  # 100次/小时
        'search': {'limit': 200, 'window': 3600},  # 200次/小时
        'upload': {'limit': 50, 'window': 3600},  # 50次/小时
    }
    
    config = default_limits.get(action, {'limit': 60, 'window': 60})
    limit = limit or config['limit']
    window = window or config['window']
    
    key = f"rate_limit:{action}:{identifier}"
    
    try:
        current_count = redis_client.get(key)
        if current_count is None:
            # 第一次请求
            redis_client.setex(key, window, 1)
            return True
        
        current_count = int(current_count)
        if current_count >= limit:
            return False
        
        # 增加计数
        redis_client.incr(key)
        return True
        
    except Exception as e:
        current_app.logger.error(f"限流检查错误: {str(e)}")
        return True  # 出错时允许通过

def is_safe_path(path: str, base_path: str) -> bool:
    """检查路径是否安全（防止路径遍历攻击）"""
    import os
    try:
        # 规范化路径
        safe_path = os.path.normpath(os.path.join(base_path, path))
        base_path = os.path.normpath(base_path)
        
        # 检查是否在基础路径内
        return safe_path.startswith(base_path)
    except Exception:
        return False

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除危险字符"""
    import re
    # 移除或替换危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename.strip()

def check_file_type_allowed(filename: str, allowed_types: list = None) -> bool:
    """检查文件类型是否允许"""
    if not allowed_types:
        # 默认允许的文件类型
        allowed_types = [
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp',
            '.psd', '.ai', '.eps', '.pdf', '.doc', '.docx', '.xls', '.xlsx',
            '.ppt', '.pptx', '.txt', '.mp4', '.avi', '.mov', '.wmv', '.flv',
            '.mkv', '.webm', '.zip', '.rar', '.7z'
        ]
    
    import os
    file_ext = os.path.splitext(filename)[1].lower()
    return file_ext in allowed_types

def encrypt_data(data: str, key: str) -> str:
    """加密数据"""
    from cryptography.fernet import Fernet
    import base64
    
    # 生成密钥
    key_bytes = hashlib.sha256(key.encode()).digest()
    key_b64 = base64.urlsafe_b64encode(key_bytes)
    
    fernet = Fernet(key_b64)
    encrypted_data = fernet.encrypt(data.encode())
    return base64.urlsafe_b64encode(encrypted_data).decode()

def decrypt_data(encrypted_data: str, key: str) -> str:
    """解密数据"""
    from cryptography.fernet import Fernet
    import base64
    
    try:
        # 生成密钥
        key_bytes = hashlib.sha256(key.encode()).digest()
        key_b64 = base64.urlsafe_b64encode(key_bytes)
        
        fernet = Fernet(key_b64)
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = fernet.decrypt(encrypted_bytes)
        return decrypted_data.decode()
    except Exception:
        return None

def create_zip_with_password(file_paths: list, zip_path: str, password: str) -> bool:
    """创建带密码的ZIP文件"""
    try:
        import pyminizip
        compression_level = 5
        
        # 准备文件列表
        files_to_zip = []
        prefixes = []
        
        for file_path in file_paths:
            if os.path.exists(file_path):
                files_to_zip.append(file_path)
                prefixes.append(os.path.basename(file_path))
        
        if not files_to_zip:
            return False
        
        # 创建加密ZIP
        pyminizip.compress_multiple(
            files_to_zip, 
            prefixes, 
            zip_path, 
            password, 
            compression_level
        )
        
        return True
        
    except Exception as e:
        current_app.logger.error(f"创建加密ZIP失败: {str(e)}")
        return False

def validate_ip_access(ip_address: str, allowed_networks: list = None) -> bool:
    """验证IP访问权限"""
    if not allowed_networks:
        return True
    
    try:
        import ipaddress
        ip = ipaddress.ip_address(ip_address)
        
        for network in allowed_networks:
            if ip in ipaddress.ip_network(network, strict=False):
                return True
        
        return False
        
    except Exception:
        return False

def get_file_signature(file_path: str) -> Optional[str]:
    """获取文件签名（前几个字节的十六进制）"""
    try:
        with open(file_path, 'rb') as f:
            signature = f.read(16)  # 读取前16字节
            return signature.hex().upper()
    except Exception:
        return None

def is_image_file(file_path: str) -> bool:
    """检查是否为图像文件（基于文件签名）"""
    signature = get_file_signature(file_path)
    if not signature:
        return False
    
    # 常见图像文件签名
    image_signatures = {
        'FFD8FF': 'JPEG',
        '89504E47': 'PNG',
        '47494638': 'GIF',
        '424D': 'BMP',
        '49492A00': 'TIFF',
        '4D4D002A': 'TIFF',
        '52494646': 'WEBP'
    }
    
    for sig in image_signatures:
        if signature.startswith(sig):
            return True
    
    return False
