"""
数据库初始化脚本
"""
import os
import sys
from datetime import datetime
from flask import Flask
from app import create_app, db
from app.models import *

def init_database():
    """初始化数据库"""
    app = create_app('development')
    
    with app.app_context():
        print("正在创建数据库表...")
        
        # 删除所有表（如果存在）
        db.drop_all()
        
        # 创建所有表
        db.create_all()
        
        print("数据库表创建完成！")
        
        # 创建默认数据
        create_default_data()
        
        print("默认数据创建完成！")

def create_default_data():
    """创建默认数据"""
    
    # 创建默认用户组
    admin_group = UserGroup(
        name='管理员',
        description='系统管理员组，拥有所有权限',
        can_read=True,
        can_write=True,
        can_delete=True,
        can_download=True,
        can_upload=True,
        can_share=True,
        max_download_size=10*1024*1024*1024,  # 10GB
        max_download_count=1000,
        daily_download_limit=10000,
        internal_access=True,
        external_access=True
    )
    
    user_group = UserGroup(
        name='普通用户',
        description='普通用户组，基础权限',
        can_read=True,
        can_write=False,
        can_delete=False,
        can_download=True,
        can_upload=False,
        can_share=False,
        max_download_size=100*1024*1024,  # 100MB
        max_download_count=50,
        daily_download_limit=100,
        internal_access=True,
        external_access=False
    )
    
    readonly_group = UserGroup(
        name='只读用户',
        description='只读用户组，仅查看权限',
        can_read=True,
        can_write=False,
        can_delete=False,
        can_download=False,
        can_upload=False,
        can_share=False,
        max_download_size=0,
        max_download_count=0,
        daily_download_limit=0,
        internal_access=True,
        external_access=False
    )
    
    db.session.add_all([admin_group, user_group, readonly_group])
    db.session.commit()
    
    # 创建默认管理员用户
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        real_name='系统管理员',
        is_active=True,
        is_admin=True,
        group_id=admin_group.id
    )
    admin_user.set_password('admin123')
    
    # 创建测试用户
    test_user = User(
        username='testuser',
        email='<EMAIL>',
        real_name='测试用户',
        is_active=True,
        is_admin=False,
        group_id=user_group.id
    )
    test_user.set_password('test123')
    
    db.session.add_all([admin_user, test_user])
    db.session.commit()
    
    # 创建默认系统配置
    default_configs = [
        # 系统基础配置
        SystemConfig(
            config_key='system_name',
            config_value='企业文件共享系统',
            config_type='string',
            description='系统名称',
            category='system',
            is_public=True
        ),
        SystemConfig(
            config_key='system_version',
            config_value='1.0.0',
            config_type='string',
            description='系统版本',
            category='system',
            is_public=True
        ),
        SystemConfig(
            config_key='max_upload_size',
            config_value='1073741824',  # 1GB
            config_type='integer',
            description='最大上传文件大小(字节)',
            category='upload'
        ),
        SystemConfig(
            config_key='allowed_file_types',
            config_value='jpg,jpeg,png,gif,bmp,tiff,tif,webp,psd,ai,eps,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,mp4,avi,mov',
            config_type='string',
            description='允许的文件类型',
            category='upload'
        ),
        
        # 搜索引擎配置
        SystemConfig(
            config_key='enable_text_search',
            config_value='true',
            config_type='boolean',
            description='启用文本搜索引擎',
            category='search'
        ),
        SystemConfig(
            config_key='enable_image_search',
            config_value='true',
            config_type='boolean',
            description='启用图像搜索引擎',
            category='search'
        ),
        
        # 下载配置
        SystemConfig(
            config_key='enable_download_encryption',
            config_value='true',
            config_type='boolean',
            description='启用下载加密',
            category='download'
        ),
        SystemConfig(
            config_key='encryption_threshold',
            config_value='3',
            config_type='integer',
            description='加密阈值(下载次数)',
            category='download'
        ),
        
        # 安全配置
        SystemConfig(
            config_key='max_login_attempts',
            config_value='5',
            config_type='integer',
            description='最大登录尝试次数',
            category='security'
        ),
        SystemConfig(
            config_key='login_timeout',
            config_value='300',
            config_type='integer',
            description='登录超时时间(秒)',
            category='security'
        ),
        
        # 监控配置
        SystemConfig(
            config_key='enable_access_log',
            config_value='true',
            config_type='boolean',
            description='启用访问日志',
            category='monitoring'
        ),
        SystemConfig(
            config_key='enable_operation_log',
            config_value='true',
            config_type='boolean',
            description='启用操作日志',
            category='monitoring'
        ),
        
        # 外网访问配置
        SystemConfig(
            config_key='enable_external_access',
            config_value='false',
            config_type='boolean',
            description='启用外网访问',
            category='network'
        ),
        SystemConfig(
            config_key='external_port',
            config_value='8080',
            config_type='integer',
            description='外网访问端口',
            category='network'
        )
    ]
    
    db.session.add_all(default_configs)
    db.session.commit()
    
    # 创建默认通知
    welcome_notification = SystemNotification(
        title='欢迎使用企业文件共享系统',
        content='系统已成功初始化，请联系管理员配置共享文件夹。',
        notification_type='info',
        is_active=True,
        is_marquee=True,
        show_on_login=True,
        show_on_homepage=True,
        is_global=True,
        priority=1,
        background_color='#e3f2fd',
        text_color='#1976d2'
    )
    
    db.session.add(welcome_notification)
    db.session.commit()
    
    print(f"默认管理员账户: admin / admin123")
    print(f"默认测试账户: testuser / test123")

if __name__ == '__main__':
    init_database()
