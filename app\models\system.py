"""
系统相关数据模型
"""
from datetime import datetime
from app import db

class SystemConfig(db.Model):
    """系统配置模型"""
    __tablename__ = 'system_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    config_key = db.Column(db.String(100), unique=True, nullable=False, comment='配置键')
    config_value = db.Column(db.Text, comment='配置值')
    config_type = db.Column(db.String(20), default='string', comment='配置类型')
    description = db.Column(db.Text, comment='配置描述')
    category = db.Column(db.String(50), comment='配置分类')
    
    # 配置属性
    is_public = db.Column(db.Bo<PERSON>, default=False, comment='是否公开配置')
    is_editable = db.Column(db.<PERSON>, default=True, comment='是否可编辑')
    requires_restart = db.Column(db.<PERSON><PERSON>, default=False, comment='是否需要重启')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_value(self):
        """获取配置值（根据类型转换）"""
        if self.config_type == 'boolean':
            return self.config_value.lower() in ('true', '1', 'yes', 'on')
        elif self.config_type == 'integer':
            try:
                return int(self.config_value)
            except (ValueError, TypeError):
                return 0
        elif self.config_type == 'float':
            try:
                return float(self.config_value)
            except (ValueError, TypeError):
                return 0.0
        elif self.config_type == 'json':
            import json
            try:
                return json.loads(self.config_value)
            except (ValueError, TypeError):
                return {}
        else:
            return self.config_value
    
    def set_value(self, value):
        """设置配置值（根据类型转换）"""
        if self.config_type == 'json':
            import json
            self.config_value = json.dumps(value, ensure_ascii=False)
        else:
            self.config_value = str(value)
    
    def __repr__(self):
        return f'<SystemConfig {self.config_key}>'

class SystemNotification(db.Model):
    """系统通知模型"""
    __tablename__ = 'system_notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False, comment='通知标题')
    content = db.Column(db.Text, nullable=False, comment='通知内容')
    notification_type = db.Column(db.String(20), default='info', comment='通知类型')
    
    # 显示设置
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    is_marquee = db.Column(db.Boolean, default=False, comment='是否滚动显示')
    show_on_login = db.Column(db.Boolean, default=False, comment='登录时显示')
    show_on_homepage = db.Column(db.Boolean, default=True, comment='首页显示')
    
    # 目标用户
    target_users = db.Column(db.Text, comment='目标用户ID列表(JSON)')
    target_groups = db.Column(db.Text, comment='目标用户组ID列表(JSON)')
    is_global = db.Column(db.Boolean, default=True, comment='是否全局通知')
    
    # 优先级和样式
    priority = db.Column(db.Integer, default=0, comment='优先级')
    background_color = db.Column(db.String(20), comment='背景颜色')
    text_color = db.Column(db.String(20), comment='文字颜色')
    
    # 附件
    has_image = db.Column(db.Boolean, default=False, comment='是否有图片')
    image_path = db.Column(db.String(500), comment='图片路径')
    
    # 时间设置
    start_time = db.Column(db.DateTime, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    
    # 统计信息
    view_count = db.Column(db.Integer, default=0, comment='查看次数')
    click_count = db.Column(db.Integer, default=0, comment='点击次数')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def is_visible(self):
        """检查通知是否可见"""
        if not self.is_active:
            return False
        
        now = datetime.utcnow()
        if self.start_time and now < self.start_time:
            return False
        if self.end_time and now > self.end_time:
            return False
        
        return True
    
    def get_target_users(self):
        """获取目标用户列表"""
        if self.is_global:
            return []
        
        import json
        try:
            return json.loads(self.target_users) if self.target_users else []
        except (ValueError, TypeError):
            return []
    
    def get_target_groups(self):
        """获取目标用户组列表"""
        if self.is_global:
            return []
        
        import json
        try:
            return json.loads(self.target_groups) if self.target_groups else []
        except (ValueError, TypeError):
            return []
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'notification_type': self.notification_type,
            'is_active': self.is_active,
            'is_marquee': self.is_marquee,
            'show_on_login': self.show_on_login,
            'show_on_homepage': self.show_on_homepage,
            'is_global': self.is_global,
            'priority': self.priority,
            'background_color': self.background_color,
            'text_color': self.text_color,
            'has_image': self.has_image,
            'image_path': self.image_path,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'view_count': self.view_count,
            'click_count': self.click_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<SystemNotification {self.title}>'

class SystemStats(db.Model):
    """系统统计模型"""
    __tablename__ = 'system_stats'
    
    id = db.Column(db.Integer, primary_key=True)
    stat_date = db.Column(db.Date, nullable=False, comment='统计日期')
    
    # 用户统计
    total_users = db.Column(db.Integer, default=0, comment='总用户数')
    active_users = db.Column(db.Integer, default=0, comment='活跃用户数')
    new_users = db.Column(db.Integer, default=0, comment='新增用户数')
    online_users = db.Column(db.Integer, default=0, comment='在线用户数')
    
    # 文件统计
    total_files = db.Column(db.Integer, default=0, comment='总文件数')
    total_size = db.Column(db.BigInteger, default=0, comment='总文件大小')
    new_files = db.Column(db.Integer, default=0, comment='新增文件数')
    deleted_files = db.Column(db.Integer, default=0, comment='删除文件数')
    
    # 访问统计
    total_visits = db.Column(db.Integer, default=0, comment='总访问次数')
    unique_visitors = db.Column(db.Integer, default=0, comment='独立访客数')
    page_views = db.Column(db.Integer, default=0, comment='页面浏览量')
    
    # 下载统计
    total_downloads = db.Column(db.Integer, default=0, comment='总下载次数')
    download_size = db.Column(db.BigInteger, default=0, comment='下载总大小')
    failed_downloads = db.Column(db.Integer, default=0, comment='失败下载次数')
    
    # 搜索统计
    total_searches = db.Column(db.Integer, default=0, comment='总搜索次数')
    text_searches = db.Column(db.Integer, default=0, comment='文本搜索次数')
    image_searches = db.Column(db.Integer, default=0, comment='图像搜索次数')
    
    # 系统性能
    avg_response_time = db.Column(db.Float, comment='平均响应时间')
    max_concurrent_users = db.Column(db.Integer, comment='最大并发用户数')
    system_uptime = db.Column(db.Float, comment='系统运行时间(小时)')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<SystemStats {self.stat_date}>'
