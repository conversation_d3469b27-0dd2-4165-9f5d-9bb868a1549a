@echo off
chcp 65001 >nul
echo ================================================
echo 企业文件共享系统 - 启动脚本
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查是否已安装依赖
if not exist "venv" (
    echo 首次运行，正在创建虚拟环境...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo 正在安装依赖包...
    pip install -r requirements.txt
    echo 依赖安装完成
) else (
    call venv\Scripts\activate.bat
)

REM 检查是否需要初始化数据库
if not exist "logs" (
    echo 正在初始化系统...
    python install.py
    if errorlevel 1 (
        echo 系统初始化失败，请检查错误信息
        pause
        exit /b 1
    )
)

echo.
echo 正在启动系统...
echo 管理员界面: http://localhost:5000/admin
echo 用户界面: http://localhost:5000/
echo.
echo 按 Ctrl+C 停止服务器
echo ================================================

REM 启动应用
python run.py

pause
