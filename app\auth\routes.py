"""
认证路由
"""
from flask import request, jsonify, current_app
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity, get_current_user
from datetime import datetime, timedelta
import uuid

from app.auth import auth_bp
from app import db
from app.models import User, UserSession, AccessLog
from app.utils.validators import validate_login_data
from app.utils.security import get_client_ip, get_user_agent, check_rate_limit
from app.utils.logger import log_access, log_operation

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据格式错误'}), 400
        
        # 验证数据
        validation_result = validate_login_data(data)
        if not validation_result['valid']:
            return jsonify({'error': validation_result['message']}), 400
        
        username = data.get('username')
        password = data.get('password')
        remember_me = data.get('remember_me', False)
        
        # 获取客户端信息
        ip_address = get_client_ip()
        user_agent = get_user_agent()
        
        # 检查限流
        if not check_rate_limit(ip_address, 'login'):
            log_access(None, ip_address, user_agent, 'POST', '/auth/login', 429)
            return jsonify({'error': '登录尝试过于频繁，请稍后再试'}), 429
        
        # 查找用户
        user = User.query.filter_by(username=username).first()
        
        if not user:
            log_access(None, ip_address, user_agent, 'POST', '/auth/login', 401)
            return jsonify({'error': '用户名或密码错误'}), 401
        
        # 检查用户状态
        if not user.can_login():
            reason = '账户已被禁用' if user.is_banned_now() else '账户未激活'
            log_operation(user.id, 'login_failed', 'user_account', f'登录失败: {reason}', 
                         ip_address=ip_address, result='failed')
            return jsonify({'error': reason}), 403
        
        # 验证密码
        if not user.check_password(password):
            user.increment_login_attempt()
            db.session.commit()
            
            log_operation(user.id, 'login_failed', 'user_account', '密码错误', 
                         ip_address=ip_address, result='failed')
            return jsonify({'error': '用户名或密码错误'}), 401
        
        # 登录成功
        user.reset_login_attempts()
        
        # 创建JWT令牌
        expires_delta = timedelta(days=30) if remember_me else timedelta(hours=24)
        access_token = create_access_token(
            identity=user,
            expires_delta=expires_delta
        )
        refresh_token = create_refresh_token(identity=user)
        
        # 创建会话记录
        session = UserSession(
            user_id=user.id,
            session_token=str(uuid.uuid4()),
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=datetime.utcnow() + expires_delta
        )
        
        db.session.add(session)
        db.session.commit()
        
        # 记录日志
        log_operation(user.id, 'login_success', 'user_account', '用户登录成功', 
                     ip_address=ip_address, result='success')
        log_access(user.id, ip_address, user_agent, 'POST', '/auth/login', 200)
        
        return jsonify({
            'message': '登录成功',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': user.to_dict(),
            'session_id': session.id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"登录错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@auth_bp.route('/admin/login', methods=['POST'])
def admin_login():
    """管理员登录"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据格式错误'}), 400
        
        # 验证数据
        validation_result = validate_login_data(data)
        if not validation_result['valid']:
            return jsonify({'error': validation_result['message']}), 400
        
        username = data.get('username')
        password = data.get('password')
        
        # 获取客户端信息
        ip_address = get_client_ip()
        user_agent = get_user_agent()
        
        # 检查限流
        if not check_rate_limit(ip_address, 'admin_login'):
            log_access(None, ip_address, user_agent, 'POST', '/auth/admin/login', 429)
            return jsonify({'error': '登录尝试过于频繁，请稍后再试'}), 429
        
        # 查找用户
        user = User.query.filter_by(username=username, is_admin=True).first()
        
        if not user:
            log_access(None, ip_address, user_agent, 'POST', '/auth/admin/login', 401)
            return jsonify({'error': '管理员账户不存在或密码错误'}), 401
        
        # 检查用户状态
        if not user.can_login():
            reason = '账户已被禁用' if user.is_banned_now() else '账户未激活'
            log_operation(user.id, 'admin_login_failed', 'admin_account', f'管理员登录失败: {reason}', 
                         ip_address=ip_address, result='failed')
            return jsonify({'error': reason}), 403
        
        # 验证密码
        if not user.check_password(password):
            user.increment_login_attempt()
            db.session.commit()
            
            log_operation(user.id, 'admin_login_failed', 'admin_account', '管理员密码错误', 
                         ip_address=ip_address, result='failed', risk_level='high')
            return jsonify({'error': '管理员账户不存在或密码错误'}), 401
        
        # 登录成功
        user.reset_login_attempts()
        
        # 创建JWT令牌（管理员会话时间较短）
        access_token = create_access_token(
            identity=user,
            expires_delta=timedelta(hours=8)
        )
        refresh_token = create_refresh_token(identity=user)
        
        # 创建会话记录
        session = UserSession(
            user_id=user.id,
            session_token=str(uuid.uuid4()),
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=datetime.utcnow() + timedelta(hours=8)
        )
        
        db.session.add(session)
        db.session.commit()
        
        # 记录日志
        log_operation(user.id, 'admin_login_success', 'admin_account', '管理员登录成功', 
                     ip_address=ip_address, result='success', risk_level='medium')
        log_access(user.id, ip_address, user_agent, 'POST', '/auth/admin/login', 200)
        
        return jsonify({
            'message': '管理员登录成功',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': user.to_dict(),
            'session_id': session.id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"管理员登录错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    try:
        current_user = get_current_user()
        ip_address = get_client_ip()
        user_agent = get_user_agent()
        
        # 获取会话ID（如果提供）
        data = request.get_json() or {}
        session_id = data.get('session_id')
        
        # 删除会话记录
        if session_id:
            session = UserSession.query.filter_by(
                id=session_id, 
                user_id=current_user.id
            ).first()
            if session:
                db.session.delete(session)
        
        db.session.commit()
        
        # 记录日志
        log_operation(current_user.id, 'logout', 'user_account', '用户登出', 
                     ip_address=ip_address, result='success')
        log_access(current_user.id, ip_address, user_agent, 'POST', '/auth/logout', 200)
        
        return jsonify({'message': '登出成功'}), 200
        
    except Exception as e:
        current_app.logger.error(f"登出错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    try:
        current_user = get_current_user()
        
        # 检查用户状态
        if not current_user.can_login():
            return jsonify({'error': '用户状态异常，请重新登录'}), 403
        
        # 创建新的访问令牌
        new_access_token = create_access_token(identity=current_user)
        
        return jsonify({
            'access_token': new_access_token,
            'user': current_user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"刷新令牌错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@auth_bp.route('/verify', methods=['GET'])
@jwt_required()
def verify_token():
    """验证令牌有效性"""
    try:
        current_user = get_current_user()
        
        # 检查用户状态
        if not current_user.can_login():
            return jsonify({'error': '用户状态异常，请重新登录'}), 403
        
        return jsonify({
            'valid': True,
            'user': current_user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"验证令牌错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500
