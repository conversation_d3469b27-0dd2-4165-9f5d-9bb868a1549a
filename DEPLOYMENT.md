# 企业文件共享系统 - 部署指南

## 系统概述

本系统是一个基于Python+MySQL的企业级文件共享系统，支持：
- 前后端分离架构
- 管理员和用户界面完全分离
- 双搜索引擎（文本搜索 + 图像识别）
- 权限控制和监控日志
- 加密下载和限流保护
- 中文支持和Windows部署

## 部署环境要求

### 软件要求
- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **Python**: 3.8 或更高版本
- **MySQL**: 5.7 或更高版本
- **浏览器**: Chrome/Firefox/Edge 最新版本

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **硬盘**: 系统盘10GB + 数据存储空间
- **网络**: 内网环境，可选外网访问

## 快速部署

### 方法一：自动安装（推荐）

1. **下载源码**
   ```bash
   # 解压源码到目标目录，例如 C:\FileShareSystem
   cd C:\FileShareSystem
   ```

2. **运行安装脚本**
   ```bash
   python install.py
   ```
   
   安装脚本会自动：
   - 检查Python版本
   - 安装所有依赖包
   - 检查MySQL连接
   - 创建数据库和表
   - 初始化默认数据
   - 创建必要目录

3. **启动系统**
   ```bash
   python run.py
   ```
   
   或者双击 `start.bat` 文件

### 方法二：手动安装

如果自动安装失败，可以按以下步骤手动安装：

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **配置MySQL数据库**
   - 启动MySQL服务
   - 创建数据库：
     ```sql
     CREATE DATABASE file_sharing_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
     ```

3. **修改配置文件**
   编辑 `config.py`，修改数据库连接信息：
   ```python
   MYSQL_HOST = 'localhost'
   MYSQL_USER = 'root'
   MYSQL_PASSWORD = '123456'  # 修改为实际密码
   MYSQL_DATABASE = 'file_sharing_system'
   ```

4. **初始化数据库**
   ```bash
   python init_db.py
   ```

5. **创建目录**
   ```bash
   mkdir uploads thumbnails temp search_index image_features logs
   ```

6. **启动系统**
   ```bash
   python run.py
   ```

## 系统配置

### 默认账户

安装完成后，系统会创建以下默认账户：

**管理员账户**
- 用户名: `admin`
- 密码: `admin123`
- 访问地址: http://localhost:5000/admin

**测试用户账户**
- 用户名: `testuser`
- 密码: `test123`
- 访问地址: http://localhost:5000/

### 首次配置步骤

1. **登录管理员界面**
   - 访问 http://localhost:5000/admin
   - 使用默认管理员账户登录

2. **修改管理员密码**
   - 进入用户管理
   - 修改admin用户密码

3. **配置用户组**
   - 根据需要创建不同权限的用户组
   - 设置各组的权限和限制

4. **添加用户**
   - 创建实际使用的用户账户
   - 分配到相应的用户组

5. **配置文件共享**
   - 设置共享文件夹路径
   - 配置访问权限和下载限制

6. **系统设置**
   - 配置系统名称和基本信息
   - 设置搜索引擎开关
   - 配置安全策略

## 生产环境部署

### 安全配置

1. **修改默认密码**
   - 更改所有默认账户密码
   - 使用强密码策略

2. **配置HTTPS**
   - 使用反向代理（如Nginx）配置SSL
   - 强制HTTPS访问

3. **防火墙设置**
   - 只开放必要端口
   - 限制管理员界面访问IP

4. **数据库安全**
   - 修改MySQL默认端口
   - 设置复杂的数据库密码
   - 限制数据库访问IP

### 性能优化

1. **数据库优化**
   ```sql
   # MySQL配置优化
   innodb_buffer_pool_size = 1G
   max_connections = 200
   query_cache_size = 64M
   ```

2. **文件存储优化**
   - 使用SSD存储提高性能
   - 定期清理临时文件
   - 配置文件压缩

3. **缓存配置**
   - 安装Redis用于缓存
   - 启用文件缓存
   - 配置缩略图缓存

### 监控和维护

1. **日志监控**
   - 定期检查系统日志
   - 监控错误和异常
   - 设置日志轮转

2. **性能监控**
   - 监控CPU和内存使用
   - 监控磁盘空间
   - 监控网络流量

3. **备份策略**
   - 定期备份数据库
   - 备份配置文件
   - 备份重要文件

## 外网访问配置

### 内网穿透

1. **使用frp**
   ```ini
   # frpc.ini
   [common]
   server_addr = your-server.com
   server_port = 7000
   
   [file-share]
   type = http
   local_port = 5000
   custom_domains = your-domain.com
   ```

2. **使用ngrok**
   ```bash
   ngrok http 5000
   ```

### 域名配置

1. **申请域名**
   - 注册域名
   - 配置DNS解析

2. **SSL证书**
   - 申请免费SSL证书
   - 配置HTTPS

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证用户名密码
   - 检查防火墙设置

2. **依赖安装失败**
   - 使用国内镜像源
   - 检查Python版本
   - 手动安装问题包

3. **文件上传失败**
   - 检查目录权限
   - 检查磁盘空间
   - 检查文件大小限制

4. **搜索功能异常**
   - 重建搜索索引
   - 检查文件权限
   - 清理缓存文件

### 日志查看

系统日志位置：
- 应用日志: `logs/system.log`
- 访问日志: 数据库 `access_logs` 表
- 操作日志: 数据库 `operation_logs` 表
- 下载日志: 数据库 `download_logs` 表

### 性能调优

1. **数据库调优**
   - 添加索引
   - 优化查询
   - 清理历史数据

2. **应用调优**
   - 增加进程数
   - 配置缓存
   - 优化文件处理

## 升级和维护

### 系统升级

1. **备份数据**
   ```bash
   mysqldump -u root -p file_sharing_system > backup.sql
   ```

2. **更新代码**
   - 下载新版本代码
   - 比较配置文件差异
   - 更新依赖包

3. **数据库迁移**
   ```bash
   python migrate_db.py
   ```

4. **重启服务**
   ```bash
   python run.py
   ```

### 定期维护

1. **每日检查**
   - 系统运行状态
   - 磁盘空间
   - 错误日志

2. **每周维护**
   - 清理临时文件
   - 检查备份
   - 更新安全补丁

3. **每月维护**
   - 性能分析
   - 容量规划
   - 安全审计

## 技术支持

如遇到问题，请按以下顺序排查：

1. 查看系统日志
2. 检查配置文件
3. 验证网络连接
4. 测试数据库连接
5. 检查文件权限

联系方式：
- 技术文档: README.md
- 问题反馈: 提交Issue
- 紧急支持: 联系系统管理员
