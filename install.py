"""
系统安装脚本
"""
import os
import sys
import subprocess
import mysql.connector
from mysql.connector import Error

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    print(f"✓ Python版本: {sys.version}")
    return True

def install_requirements():
    """安装依赖包"""
    print("正在安装Python依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 依赖包安装失败 - {e}")
        return False

def check_mysql_connection():
    """检查MySQL连接"""
    print("检查MySQL连接...")
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✓ MySQL连接成功，版本: {version[0]}")
            
            # 创建数据库
            cursor.execute("CREATE DATABASE IF NOT EXISTS file_sharing_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✓ 数据库创建成功")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"错误: MySQL连接失败 - {e}")
        print("请确保:")
        print("1. MySQL服务已启动")
        print("2. 用户名为 'root'，密码为 '123456'")
        print("3. 或者修改 config.py 中的数据库配置")
        return False

def create_directories():
    """创建必要的目录"""
    print("创建系统目录...")
    directories = [
        'uploads',
        'thumbnails',
        'temp',
        'search_index',
        'image_features',
        'logs',
        'thumbnails/small',
        'thumbnails/medium',
        'thumbnails/large',
        'thumbnails/xlarge'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"✓ 创建目录: {directory}")
    
    return True

def initialize_database():
    """初始化数据库"""
    print("初始化数据库...")
    try:
        # 运行数据库初始化脚本
        subprocess.check_call([sys.executable, "init_db.py"])
        print("✓ 数据库初始化完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 数据库初始化失败 - {e}")
        return False

def create_config_file():
    """创建配置文件"""
    print("创建配置文件...")
    
    # 创建 .env 文件
    env_content = """# 环境配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=jwt-secret-change-in-production

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=file_sharing_system

# 文件存储配置
UPLOAD_FOLDER=uploads
THUMBNAIL_FOLDER=thumbnails
TEMP_FOLDER=temp

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/system.log
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✓ 配置文件创建完成")
    return True

def main():
    """主安装流程"""
    print("=" * 60)
    print("企业文件共享系统 - 安装程序")
    print("=" * 60)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_requirements),
        ("检查MySQL连接", check_mysql_connection),
        ("创建系统目录", create_directories),
        ("创建配置文件", create_config_file),
        ("初始化数据库", initialize_database),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"安装失败: {step_name}")
            return False
    
    print("\n" + "=" * 60)
    print("安装完成！")
    print("=" * 60)
    print("默认管理员账户:")
    print("  用户名: admin")
    print("  密码: admin123")
    print()
    print("默认测试账户:")
    print("  用户名: testuser")
    print("  密码: test123")
    print()
    print("启动系统:")
    print("  python run.py")
    print()
    print("访问地址:")
    print("  管理员界面: http://localhost:5000/admin")
    print("  用户界面: http://localhost:5000/")
    print("=" * 60)
    
    return True

if __name__ == '__main__':
    if main():
        sys.exit(0)
    else:
        sys.exit(1)
