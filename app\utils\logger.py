"""
日志记录工具
"""
from datetime import datetime
from flask import current_app
from app import db
from app.models import AccessLog, OperationLog, DownloadLog, SearchLog
from typing import Optional

def log_access(user_id: Optional[int], ip_address: str, user_agent: str, 
               method: str, url: str, status_code: int, 
               response_size: Optional[int] = None, response_time: Optional[float] = None,
               access_type: str = 'internal'):
    """记录访问日志"""
    try:
        access_log = AccessLog(
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=method,
            request_url=url,
            response_status=status_code,
            response_size=response_size,
            response_time=response_time,
            access_type=access_type
        )
        
        db.session.add(access_log)
        db.session.commit()
        
    except Exception as e:
        current_app.logger.error(f"记录访问日志失败: {str(e)}")
        db.session.rollback()

def log_operation(user_id: int, operation_type: str, operation_target: str,
                  operation_detail: str, file_path: Optional[str] = None,
                  file_name: Optional[str] = None, file_size: Optional[int] = None,
                  result: str = 'success', error_message: Optional[str] = None,
                  ip_address: Optional[str] = None, user_agent: Optional[str] = None,
                  risk_level: str = 'low', is_sensitive: bool = False):
    """记录操作日志"""
    try:
        operation_log = OperationLog(
            user_id=user_id,
            operation_type=operation_type,
            operation_target=operation_target,
            operation_detail=operation_detail,
            file_path=file_path,
            file_name=file_name,
            file_size=file_size,
            operation_result=result,
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent,
            risk_level=risk_level,
            is_sensitive=is_sensitive
        )
        
        db.session.add(operation_log)
        db.session.commit()
        
        # 如果是高风险或敏感操作，记录到应用日志
        if risk_level == 'high' or is_sensitive:
            current_app.logger.warning(
                f"敏感操作: 用户{user_id} {operation_type} {operation_target} - {operation_detail}"
            )
        
    except Exception as e:
        current_app.logger.error(f"记录操作日志失败: {str(e)}")
        db.session.rollback()

def log_download(user_id: int, download_type: str, file_count: int = 1,
                 total_size: Optional[int] = None, file_names: Optional[str] = None,
                 file_paths: Optional[str] = None, file_id: Optional[int] = None,
                 is_encrypted: bool = False, encryption_password: Optional[str] = None,
                 download_count: int = 0, ip_address: Optional[str] = None,
                 user_agent: Optional[str] = None):
    """记录下载日志"""
    try:
        download_log = DownloadLog(
            user_id=user_id,
            file_id=file_id,
            download_type=download_type,
            file_count=file_count,
            total_size=total_size,
            file_names=file_names,
            file_paths=file_paths,
            download_status='started',
            is_encrypted=is_encrypted,
            encryption_password=encryption_password,
            download_count=download_count,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        db.session.add(download_log)
        db.session.commit()
        
        return download_log.id
        
    except Exception as e:
        current_app.logger.error(f"记录下载日志失败: {str(e)}")
        db.session.rollback()
        return None

def update_download_log(log_id: int, status: str, progress: float = None,
                       completed_at: datetime = None, error_message: str = None):
    """更新下载日志"""
    try:
        download_log = DownloadLog.query.get(log_id)
        if download_log:
            download_log.download_status = status
            if progress is not None:
                download_log.download_progress = progress
            if completed_at:
                download_log.completed_at = completed_at
                download_log.calculate_duration()
            
            db.session.commit()
            
    except Exception as e:
        current_app.logger.error(f"更新下载日志失败: {str(e)}")
        db.session.rollback()

def log_search(user_id: Optional[int], search_query: str, search_type: str,
               search_engine: str, result_count: int, search_time: float,
               file_types: Optional[str] = None, size_range: Optional[str] = None,
               date_range: Optional[str] = None, ip_address: Optional[str] = None,
               user_agent: Optional[str] = None, is_sensitive: bool = False,
               blocked_keywords: Optional[str] = None):
    """记录搜索日志"""
    try:
        search_log = SearchLog(
            user_id=user_id,
            search_query=search_query,
            search_type=search_type,
            search_engine=search_engine,
            result_count=result_count,
            search_time=search_time,
            file_types=file_types,
            size_range=size_range,
            date_range=date_range,
            ip_address=ip_address,
            user_agent=user_agent,
            is_sensitive=is_sensitive,
            blocked_keywords=blocked_keywords
        )
        
        db.session.add(search_log)
        db.session.commit()
        
        # 如果是敏感搜索，记录到应用日志
        if is_sensitive:
            current_app.logger.warning(
                f"敏感搜索: 用户{user_id} 搜索 '{search_query}' - 被阻止关键词: {blocked_keywords}"
            )
        
    except Exception as e:
        current_app.logger.error(f"记录搜索日志失败: {str(e)}")
        db.session.rollback()

def get_user_activity_stats(user_id: int, days: int = 30) -> dict:
    """获取用户活动统计"""
    try:
        from datetime import timedelta
        from sqlalchemy import func
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 访问统计
        access_count = AccessLog.query.filter(
            AccessLog.user_id == user_id,
            AccessLog.created_at >= start_date
        ).count()
        
        # 下载统计
        download_stats = db.session.query(
            func.count(DownloadLog.id).label('count'),
            func.sum(DownloadLog.total_size).label('total_size')
        ).filter(
            DownloadLog.user_id == user_id,
            DownloadLog.started_at >= start_date
        ).first()
        
        # 搜索统计
        search_count = SearchLog.query.filter(
            SearchLog.user_id == user_id,
            SearchLog.created_at >= start_date
        ).count()
        
        # 操作统计
        operation_count = OperationLog.query.filter(
            OperationLog.user_id == user_id,
            OperationLog.created_at >= start_date
        ).count()
        
        return {
            'access_count': access_count,
            'download_count': download_stats.count or 0,
            'download_size': download_stats.total_size or 0,
            'search_count': search_count,
            'operation_count': operation_count
        }
        
    except Exception as e:
        current_app.logger.error(f"获取用户活动统计失败: {str(e)}")
        return {
            'access_count': 0,
            'download_count': 0,
            'download_size': 0,
            'search_count': 0,
            'operation_count': 0
        }

def get_system_activity_stats(days: int = 30) -> dict:
    """获取系统活动统计"""
    try:
        from datetime import timedelta
        from sqlalchemy import func, distinct
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 总访问量
        total_visits = AccessLog.query.filter(
            AccessLog.created_at >= start_date
        ).count()
        
        # 独立访客
        unique_visitors = db.session.query(
            func.count(distinct(AccessLog.ip_address))
        ).filter(
            AccessLog.created_at >= start_date
        ).scalar()
        
        # 下载统计
        download_stats = db.session.query(
            func.count(DownloadLog.id).label('count'),
            func.sum(DownloadLog.total_size).label('total_size')
        ).filter(
            DownloadLog.started_at >= start_date
        ).first()
        
        # 搜索统计
        search_stats = db.session.query(
            func.count(SearchLog.id).label('total'),
            func.sum(func.case([(SearchLog.search_type == 'text', 1)], else_=0)).label('text'),
            func.sum(func.case([(SearchLog.search_type == 'image', 1)], else_=0)).label('image')
        ).filter(
            SearchLog.created_at >= start_date
        ).first()
        
        # 活跃用户
        active_users = db.session.query(
            func.count(distinct(AccessLog.user_id))
        ).filter(
            AccessLog.created_at >= start_date,
            AccessLog.user_id.isnot(None)
        ).scalar()
        
        return {
            'total_visits': total_visits,
            'unique_visitors': unique_visitors or 0,
            'download_count': download_stats.count or 0,
            'download_size': download_stats.total_size or 0,
            'search_count': search_stats.total or 0,
            'text_search_count': search_stats.text or 0,
            'image_search_count': search_stats.image or 0,
            'active_users': active_users or 0
        }
        
    except Exception as e:
        current_app.logger.error(f"获取系统活动统计失败: {str(e)}")
        return {
            'total_visits': 0,
            'unique_visitors': 0,
            'download_count': 0,
            'download_size': 0,
            'search_count': 0,
            'text_search_count': 0,
            'image_search_count': 0,
            'active_users': 0
        }
