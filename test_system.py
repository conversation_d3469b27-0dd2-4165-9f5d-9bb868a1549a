"""
系统测试脚本
"""
import requests
import json
import sys

def test_system():
    """测试系统基本功能"""
    base_url = "http://localhost:5000"
    
    print("=" * 50)
    print("企业文件共享系统 - 功能测试")
    print("=" * 50)
    
    # 1. 测试健康检查
    print("1. 测试系统健康检查...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            print("✓ 系统健康检查通过")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到系统: {e}")
        print("请确保系统已启动 (python run.py)")
        return False
    
    # 2. 测试系统信息
    print("2. 测试系统信息获取...")
    try:
        response = requests.get(f"{base_url}/api/system/info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 系统名称: {data.get('system_name', 'N/A')}")
            print(f"✓ 系统版本: {data.get('system_version', 'N/A')}")
        else:
            print(f"✗ 获取系统信息失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 获取系统信息异常: {e}")
    
    # 3. 测试管理员登录
    print("3. 测试管理员登录...")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(
            f"{base_url}/auth/admin/login", 
            json=login_data,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            admin_token = data.get('access_token')
            print("✓ 管理员登录成功")
            
            # 4. 测试获取用户列表
            print("4. 测试获取用户列表...")
            headers = {"Authorization": f"Bearer {admin_token}"}
            response = requests.get(
                f"{base_url}/admin/users",
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                user_count = len(data.get('users', []))
                print(f"✓ 获取用户列表成功，共 {user_count} 个用户")
            else:
                print(f"✗ 获取用户列表失败: {response.status_code}")
            
            # 5. 测试获取用户组列表
            print("5. 测试获取用户组列表...")
            response = requests.get(
                f"{base_url}/admin/groups",
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                group_count = len(data.get('groups', []))
                print(f"✓ 获取用户组列表成功，共 {group_count} 个用户组")
            else:
                print(f"✗ 获取用户组列表失败: {response.status_code}")
                
        else:
            print(f"✗ 管理员登录失败: {response.status_code}")
            if response.status_code == 401:
                print("  请检查默认管理员账户是否正确创建")
            return False
            
    except Exception as e:
        print(f"✗ 管理员登录异常: {e}")
        return False
    
    # 6. 测试普通用户登录
    print("6. 测试普通用户登录...")
    try:
        login_data = {
            "username": "testuser",
            "password": "test123"
        }
        response = requests.post(
            f"{base_url}/auth/login", 
            json=login_data,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            user_token = data.get('access_token')
            print("✓ 普通用户登录成功")
            
            # 7. 测试获取用户资料
            print("7. 测试获取用户资料...")
            headers = {"Authorization": f"Bearer {user_token}"}
            response = requests.get(
                f"{base_url}/api/user/profile",
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                username = data.get('profile', {}).get('username', 'N/A')
                print(f"✓ 获取用户资料成功，用户名: {username}")
            else:
                print(f"✗ 获取用户资料失败: {response.status_code}")
            
            # 8. 测试获取通知
            print("8. 测试获取通知...")
            response = requests.get(
                f"{base_url}/api/notifications",
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                notification_count = len(data.get('notifications', []))
                print(f"✓ 获取通知成功，共 {notification_count} 条通知")
            else:
                print(f"✗ 获取通知失败: {response.status_code}")
                
        else:
            print(f"✗ 普通用户登录失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 普通用户登录异常: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✓ 所有基础功能测试通过！")
    print("=" * 50)
    print("系统访问地址:")
    print(f"  管理员界面: {base_url}/admin")
    print(f"  用户界面: {base_url}/")
    print(f"  API健康检查: {base_url}/api/health")
    print("=" * 50)
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    try:
        import mysql.connector
        from config import Config
        
        connection = mysql.connector.connect(
            host=Config.MYSQL_HOST,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"✓ 数据库连接成功，用户表中有 {user_count} 个用户")
            cursor.close()
            connection.close()
            return True
        else:
            print("✗ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据库连接异常: {e}")
        return False

if __name__ == '__main__':
    print("开始系统测试...\n")
    
    # 测试数据库连接
    if not test_database_connection():
        print("数据库连接测试失败，请检查配置")
        sys.exit(1)
    
    print()
    
    # 测试系统功能
    if test_system():
        print("\n系统测试完成，所有功能正常！")
        sys.exit(0)
    else:
        print("\n系统测试失败，请检查错误信息")
        sys.exit(1)
