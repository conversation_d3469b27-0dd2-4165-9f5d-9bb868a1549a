"""
应用程序启动文件
"""
import os
import sys
from app import create_app, db
from flask_migrate import upgrade

def create_tables():
    """创建数据库表"""
    with app.app_context():
        db.create_all()
        print("数据库表创建完成")

if __name__ == '__main__':
    # 设置环境变量
    os.environ.setdefault('FLASK_ENV', 'development')
    
    # 创建应用
    app = create_app()
    
    # 检查是否需要初始化数据库
    if len(sys.argv) > 1 and sys.argv[1] == 'init-db':
        print("正在初始化数据库...")
        exec(open('init_db.py').read())
        print("数据库初始化完成！")
        sys.exit(0)
    
    # 启动应用
    with app.app_context():
        # 确保数据库表存在
        try:
            db.create_all()
        except Exception as e:
            print(f"数据库连接错误: {e}")
            print("请确保MySQL服务已启动，并且数据库配置正确")
            sys.exit(1)
    
    print("=" * 50)
    print("企业文件共享系统")
    print("=" * 50)
    print(f"管理员界面: http://localhost:5000/admin")
    print(f"用户界面: http://localhost:5000/")
    print(f"API文档: http://localhost:5000/api")
    print("=" * 50)
    
    # 启动开发服务器
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
