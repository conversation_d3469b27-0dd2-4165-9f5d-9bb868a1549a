"""
管理员路由
"""
from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_current_user
from datetime import datetime, timedelta
from sqlalchemy import func, desc, or_

from app.admin import admin_bp
from app import db
from app.models import (User, UserGroup, FileInfo, FileShare, FilePermission,
                       AccessLog, OperationLog, DownloadLog, SearchLog,
                       SystemConfig, SystemNotification, SystemStats)
from app.utils.validators import (validate_user_data, validate_group_data,
                                validate_file_share_data, validate_notification_data)
from app.utils.security import get_client_ip, get_user_agent, generate_random_password
from app.utils.logger import log_operation, get_system_activity_stats

def admin_required(f):
    """管理员权限装饰器"""
    def decorated_function(*args, **kwargs):
        current_user = get_current_user()
        if not current_user or not current_user.is_admin:
            return jsonify({'error': '需要管理员权限'}), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# ==================== 用户管理 ====================

@admin_bp.route('/users', methods=['GET'])
@jwt_required()
@admin_required
def get_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '').strip()
        group_id = request.args.get('group_id', type=int)
        is_active = request.args.get('is_active', type=bool)
        
        query = User.query
        
        # 搜索过滤
        if search:
            query = query.filter(or_(
                User.username.contains(search),
                User.real_name.contains(search),
                User.email.contains(search),
                User.department.contains(search)
            ))
        
        # 用户组过滤
        if group_id:
            query = query.filter(User.group_id == group_id)
        
        # 状态过滤
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        # 排序
        query = query.order_by(desc(User.created_at))
        
        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        users = []
        for user in pagination.items:
            user_dict = user.to_dict()
            # 添加统计信息
            user_dict['last_login_str'] = user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录'
            user_dict['is_banned_now'] = user.is_banned_now()
            users.append(user_dict)
        
        return jsonify({
            'users': users,
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"获取用户列表错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@admin_bp.route('/users', methods=['POST'])
@jwt_required()
@admin_required
def create_user():
    """创建用户"""
    try:
        current_user = get_current_user()
        data = request.get_json()
        
        # 验证数据
        validation_result = validate_user_data(data)
        if not validation_result['valid']:
            return jsonify({'error': validation_result['message']}), 400
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'error': '用户名已存在'}), 400
        
        # 检查邮箱是否已存在
        if data.get('email') and User.query.filter_by(email=data['email']).first():
            return jsonify({'error': '邮箱已存在'}), 400
        
        # 检查用户组是否存在
        group = UserGroup.query.get(data['group_id'])
        if not group:
            return jsonify({'error': '用户组不存在'}), 400
        
        # 创建用户
        user = User(
            username=data['username'],
            email=data.get('email'),
            real_name=data.get('real_name'),
            phone=data.get('phone'),
            department=data.get('department'),
            is_active=data.get('is_active', True),
            is_admin=data.get('is_admin', False),
            group_id=data['group_id']
        )
        
        # 设置密码
        password = data.get('password')
        if not password:
            password = generate_random_password()
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        # 记录操作日志
        log_operation(
            current_user.id, 'create_user', 'user_management',
            f'创建用户: {user.username}',
            ip_address=get_client_ip(),
            user_agent=get_user_agent(),
            result='success'
        )
        
        result = user.to_dict()
        if not data.get('password'):
            result['generated_password'] = password
        
        return jsonify({
            'message': '用户创建成功',
            'user': result
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"创建用户错误: {str(e)}")
        db.session.rollback()
        return jsonify({'error': '服务器内部错误'}), 500

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_user(user_id):
    """更新用户"""
    try:
        current_user = get_current_user()
        data = request.get_json()
        
        # 验证数据
        validation_result = validate_user_data(data, is_update=True)
        if not validation_result['valid']:
            return jsonify({'error': validation_result['message']}), 400
        
        # 查找用户
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        # 检查用户名是否已被其他用户使用
        if data.get('username') and data['username'] != user.username:
            if User.query.filter_by(username=data['username']).first():
                return jsonify({'error': '用户名已存在'}), 400
        
        # 检查邮箱是否已被其他用户使用
        if data.get('email') and data['email'] != user.email:
            if User.query.filter_by(email=data['email']).first():
                return jsonify({'error': '邮箱已存在'}), 400
        
        # 检查用户组是否存在
        if data.get('group_id'):
            group = UserGroup.query.get(data['group_id'])
            if not group:
                return jsonify({'error': '用户组不存在'}), 400
        
        # 更新用户信息
        old_data = user.to_dict()
        
        if data.get('username'):
            user.username = data['username']
        if data.get('email') is not None:
            user.email = data['email']
        if data.get('real_name') is not None:
            user.real_name = data['real_name']
        if data.get('phone') is not None:
            user.phone = data['phone']
        if data.get('department') is not None:
            user.department = data['department']
        if data.get('is_active') is not None:
            user.is_active = data['is_active']
        if data.get('is_admin') is not None:
            user.is_admin = data['is_admin']
        if data.get('group_id'):
            user.group_id = data['group_id']
        
        # 更新密码
        if data.get('password'):
            user.set_password(data['password'])
        
        # 解除禁用
        if data.get('unban'):
            user.is_banned = False
            user.ban_until = None
            user.login_attempts = 0
        
        db.session.commit()
        
        # 记录操作日志
        changes = []
        for key, value in data.items():
            if key in old_data and old_data[key] != value:
                changes.append(f"{key}: {old_data[key]} -> {value}")
        
        log_operation(
            current_user.id, 'update_user', 'user_management',
            f'更新用户: {user.username}, 变更: {", ".join(changes)}',
            ip_address=get_client_ip(),
            user_agent=get_user_agent(),
            result='success'
        )
        
        return jsonify({
            'message': '用户更新成功',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"更新用户错误: {str(e)}")
        db.session.rollback()
        return jsonify({'error': '服务器内部错误'}), 500

@admin_bp.route('/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_user(user_id):
    """删除用户"""
    try:
        current_user = get_current_user()
        
        # 查找用户
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        # 不能删除自己
        if user.id == current_user.id:
            return jsonify({'error': '不能删除自己的账户'}), 400
        
        username = user.username
        
        # 删除用户（级联删除相关记录）
        db.session.delete(user)
        db.session.commit()
        
        # 记录操作日志
        log_operation(
            current_user.id, 'delete_user', 'user_management',
            f'删除用户: {username}',
            ip_address=get_client_ip(),
            user_agent=get_user_agent(),
            result='success',
            risk_level='high'
        )
        
        return jsonify({'message': '用户删除成功'}), 200

    except Exception as e:
        current_app.logger.error(f"删除用户错误: {str(e)}")
        db.session.rollback()
        return jsonify({'error': '服务器内部错误'}), 500

# ==================== 用户组管理 ====================

@admin_bp.route('/groups', methods=['GET'])
@jwt_required()
@admin_required
def get_groups():
    """获取用户组列表"""
    try:
        groups = UserGroup.query.order_by(UserGroup.created_at).all()

        result = []
        for group in groups:
            group_dict = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'can_read': group.can_read,
                'can_write': group.can_write,
                'can_delete': group.can_delete,
                'can_download': group.can_download,
                'can_upload': group.can_upload,
                'can_share': group.can_share,
                'max_download_size': group.max_download_size,
                'max_download_count': group.max_download_count,
                'daily_download_limit': group.daily_download_limit,
                'internal_access': group.internal_access,
                'external_access': group.external_access,
                'user_count': group.users.count(),
                'created_at': group.created_at.isoformat(),
                'updated_at': group.updated_at.isoformat()
            }
            result.append(group_dict)

        return jsonify({'groups': result}), 200

    except Exception as e:
        current_app.logger.error(f"获取用户组列表错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@admin_bp.route('/groups', methods=['POST'])
@jwt_required()
@admin_required
def create_group():
    """创建用户组"""
    try:
        current_user = get_current_user()
        data = request.get_json()

        # 验证数据
        validation_result = validate_group_data(data)
        if not validation_result['valid']:
            return jsonify({'error': validation_result['message']}), 400

        # 检查组名是否已存在
        if UserGroup.query.filter_by(name=data['name']).first():
            return jsonify({'error': '用户组名称已存在'}), 400

        # 创建用户组
        group = UserGroup(
            name=data['name'],
            description=data.get('description'),
            can_read=data.get('can_read', True),
            can_write=data.get('can_write', False),
            can_delete=data.get('can_delete', False),
            can_download=data.get('can_download', True),
            can_upload=data.get('can_upload', False),
            can_share=data.get('can_share', False),
            max_download_size=data.get('max_download_size', 100*1024*1024),
            max_download_count=data.get('max_download_count', 50),
            daily_download_limit=data.get('daily_download_limit', 100),
            internal_access=data.get('internal_access', True),
            external_access=data.get('external_access', False)
        )

        db.session.add(group)
        db.session.commit()

        # 记录操作日志
        log_operation(
            current_user.id, 'create_group', 'group_management',
            f'创建用户组: {group.name}',
            ip_address=get_client_ip(),
            user_agent=get_user_agent(),
            result='success'
        )

        return jsonify({
            'message': '用户组创建成功',
            'group': {
                'id': group.id,
                'name': group.name,
                'description': group.description
            }
        }), 201

    except Exception as e:
        current_app.logger.error(f"创建用户组错误: {str(e)}")
        db.session.rollback()
        return jsonify({'error': '服务器内部错误'}), 500

@admin_bp.route('/groups/<int:group_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_group(group_id):
    """更新用户组"""
    try:
        current_user = get_current_user()
        data = request.get_json()

        # 验证数据
        validation_result = validate_group_data(data)
        if not validation_result['valid']:
            return jsonify({'error': validation_result['message']}), 400

        # 查找用户组
        group = UserGroup.query.get(group_id)
        if not group:
            return jsonify({'error': '用户组不存在'}), 404

        # 检查组名是否已被其他组使用
        if data.get('name') and data['name'] != group.name:
            if UserGroup.query.filter_by(name=data['name']).first():
                return jsonify({'error': '用户组名称已存在'}), 400

        # 更新用户组信息
        old_name = group.name

        if data.get('name'):
            group.name = data['name']
        if data.get('description') is not None:
            group.description = data['description']

        # 更新权限
        permissions = ['can_read', 'can_write', 'can_delete', 'can_download', 'can_upload', 'can_share']
        for perm in permissions:
            if data.get(perm) is not None:
                setattr(group, perm, data[perm])

        # 更新限制
        limits = ['max_download_size', 'max_download_count', 'daily_download_limit']
        for limit in limits:
            if data.get(limit) is not None:
                setattr(group, limit, data[limit])

        # 更新访问控制
        access_controls = ['internal_access', 'external_access']
        for control in access_controls:
            if data.get(control) is not None:
                setattr(group, control, data[control])

        db.session.commit()

        # 记录操作日志
        log_operation(
            current_user.id, 'update_group', 'group_management',
            f'更新用户组: {old_name} -> {group.name}',
            ip_address=get_client_ip(),
            user_agent=get_user_agent(),
            result='success'
        )

        return jsonify({
            'message': '用户组更新成功',
            'group': {
                'id': group.id,
                'name': group.name,
                'description': group.description
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"更新用户组错误: {str(e)}")
        db.session.rollback()
        return jsonify({'error': '服务器内部错误'}), 500
