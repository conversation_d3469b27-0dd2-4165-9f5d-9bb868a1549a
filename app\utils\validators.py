"""
数据验证工具
"""
import re
from typing import Dict, Any

def validate_login_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证登录数据"""
    if not isinstance(data, dict):
        return {'valid': False, 'message': '数据格式错误'}
    
    username = data.get('username', '').strip()
    password = data.get('password', '')
    
    if not username:
        return {'valid': False, 'message': '用户名不能为空'}
    
    if not password:
        return {'valid': False, 'message': '密码不能为空'}
    
    if len(username) < 3 or len(username) > 50:
        return {'valid': False, 'message': '用户名长度必须在3-50个字符之间'}
    
    if len(password) < 6:
        return {'valid': False, 'message': '密码长度不能少于6个字符'}
    
    # 用户名格式验证（字母、数字、下划线）
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        return {'valid': False, 'message': '用户名只能包含字母、数字和下划线'}
    
    return {'valid': True, 'message': '验证通过'}

def validate_user_data(data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
    """验证用户数据"""
    if not isinstance(data, dict):
        return {'valid': False, 'message': '数据格式错误'}
    
    # 用户名验证（新建时必须）
    username = data.get('username', '').strip()
    if not is_update and not username:
        return {'valid': False, 'message': '用户名不能为空'}
    
    if username:
        if len(username) < 3 or len(username) > 50:
            return {'valid': False, 'message': '用户名长度必须在3-50个字符之间'}
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return {'valid': False, 'message': '用户名只能包含字母、数字和下划线'}
    
    # 密码验证（新建时必须）
    password = data.get('password', '')
    if not is_update and not password:
        return {'valid': False, 'message': '密码不能为空'}
    
    if password:
        if len(password) < 6:
            return {'valid': False, 'message': '密码长度不能少于6个字符'}
        
        # 密码强度检查
        if not re.search(r'[A-Za-z]', password) or not re.search(r'[0-9]', password):
            return {'valid': False, 'message': '密码必须包含字母和数字'}
    
    # 邮箱验证
    email = data.get('email', '').strip()
    if email:
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return {'valid': False, 'message': '邮箱格式不正确'}
    
    # 真实姓名验证
    real_name = data.get('real_name', '').strip()
    if real_name and len(real_name) > 100:
        return {'valid': False, 'message': '真实姓名长度不能超过100个字符'}
    
    # 电话号码验证
    phone = data.get('phone', '').strip()
    if phone:
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, phone):
            return {'valid': False, 'message': '手机号码格式不正确'}
    
    # 部门验证
    department = data.get('department', '').strip()
    if department and len(department) > 100:
        return {'valid': False, 'message': '部门名称长度不能超过100个字符'}
    
    # 用户组验证
    group_id = data.get('group_id')
    if group_id is not None:
        try:
            group_id = int(group_id)
            if group_id <= 0:
                return {'valid': False, 'message': '用户组ID必须为正整数'}
        except (ValueError, TypeError):
            return {'valid': False, 'message': '用户组ID格式错误'}
    
    return {'valid': True, 'message': '验证通过'}

def validate_group_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证用户组数据"""
    if not isinstance(data, dict):
        return {'valid': False, 'message': '数据格式错误'}
    
    # 组名验证
    name = data.get('name', '').strip()
    if not name:
        return {'valid': False, 'message': '组名不能为空'}
    
    if len(name) < 2 or len(name) > 50:
        return {'valid': False, 'message': '组名长度必须在2-50个字符之间'}
    
    # 描述验证
    description = data.get('description', '').strip()
    if description and len(description) > 500:
        return {'valid': False, 'message': '描述长度不能超过500个字符'}
    
    # 权限验证
    permissions = ['can_read', 'can_write', 'can_delete', 'can_download', 'can_upload', 'can_share']
    for perm in permissions:
        value = data.get(perm)
        if value is not None and not isinstance(value, bool):
            return {'valid': False, 'message': f'{perm}必须为布尔值'}
    
    # 限制验证
    limits = ['max_download_size', 'max_download_count', 'daily_download_limit']
    for limit in limits:
        value = data.get(limit)
        if value is not None:
            try:
                value = int(value)
                if value < 0:
                    return {'valid': False, 'message': f'{limit}不能为负数'}
            except (ValueError, TypeError):
                return {'valid': False, 'message': f'{limit}必须为整数'}
    
    # 访问控制验证
    access_controls = ['internal_access', 'external_access']
    for control in access_controls:
        value = data.get(control)
        if value is not None and not isinstance(value, bool):
            return {'valid': False, 'message': f'{control}必须为布尔值'}
    
    return {'valid': True, 'message': '验证通过'}

def validate_file_share_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证文件共享数据"""
    if not isinstance(data, dict):
        return {'valid': False, 'message': '数据格式错误'}
    
    # 共享名称验证
    share_name = data.get('share_name', '').strip()
    if not share_name:
        return {'valid': False, 'message': '共享名称不能为空'}
    
    if len(share_name) < 2 or len(share_name) > 100:
        return {'valid': False, 'message': '共享名称长度必须在2-100个字符之间'}
    
    # 共享路径验证
    share_path = data.get('share_path', '').strip()
    if not share_path:
        return {'valid': False, 'message': '共享路径不能为空'}
    
    # 描述验证
    description = data.get('description', '').strip()
    if description and len(description) > 500:
        return {'valid': False, 'message': '描述长度不能超过500个字符'}
    
    # 布尔值验证
    boolean_fields = [
        'is_active', 'internal_access', 'external_access', 'require_auth',
        'allow_read', 'allow_write', 'allow_delete', 'allow_download', 
        'allow_upload', 'enable_encryption', 'enable_monitoring'
    ]
    
    for field in boolean_fields:
        value = data.get(field)
        if value is not None and not isinstance(value, bool):
            return {'valid': False, 'message': f'{field}必须为布尔值'}
    
    # 数值验证
    numeric_fields = ['max_download_size', 'max_download_count', 'encryption_threshold']
    for field in numeric_fields:
        value = data.get(field)
        if value is not None:
            try:
                value = int(value)
                if value < 0:
                    return {'valid': False, 'message': f'{field}不能为负数'}
            except (ValueError, TypeError):
                return {'valid': False, 'message': f'{field}必须为整数'}
    
    return {'valid': True, 'message': '验证通过'}

def validate_notification_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证通知数据"""
    if not isinstance(data, dict):
        return {'valid': False, 'message': '数据格式错误'}
    
    # 标题验证
    title = data.get('title', '').strip()
    if not title:
        return {'valid': False, 'message': '通知标题不能为空'}
    
    if len(title) > 200:
        return {'valid': False, 'message': '通知标题长度不能超过200个字符'}
    
    # 内容验证
    content = data.get('content', '').strip()
    if not content:
        return {'valid': False, 'message': '通知内容不能为空'}
    
    if len(content) > 2000:
        return {'valid': False, 'message': '通知内容长度不能超过2000个字符'}
    
    # 通知类型验证
    notification_type = data.get('notification_type', 'info')
    valid_types = ['info', 'warning', 'error', 'success']
    if notification_type not in valid_types:
        return {'valid': False, 'message': f'通知类型必须为: {", ".join(valid_types)}'}
    
    # 布尔值验证
    boolean_fields = [
        'is_active', 'is_marquee', 'show_on_login', 
        'show_on_homepage', 'is_global', 'has_image'
    ]
    
    for field in boolean_fields:
        value = data.get(field)
        if value is not None and not isinstance(value, bool):
            return {'valid': False, 'message': f'{field}必须为布尔值'}
    
    # 优先级验证
    priority = data.get('priority')
    if priority is not None:
        try:
            priority = int(priority)
            if priority < 0 or priority > 10:
                return {'valid': False, 'message': '优先级必须在0-10之间'}
        except (ValueError, TypeError):
            return {'valid': False, 'message': '优先级必须为整数'}
    
    # 颜色验证
    color_fields = ['background_color', 'text_color']
    color_pattern = r'^#[0-9A-Fa-f]{6}$'
    
    for field in color_fields:
        value = data.get(field)
        if value and not re.match(color_pattern, value):
            return {'valid': False, 'message': f'{field}必须为有效的十六进制颜色值'}
    
    return {'valid': True, 'message': '验证通过'}
