"""
文件相关数据模型
"""
from datetime import datetime
from app import db
import os
import hashlib

class FileInfo(db.Model):
    """文件信息模型"""
    __tablename__ = 'file_info'
    
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False, comment='文件名')
    original_path = db.Column(db.Text, nullable=False, comment='原始路径')
    relative_path = db.Column(db.Text, nullable=False, comment='相对路径')
    file_hash = db.Column(db.String(64), nullable=False, comment='文件哈希值')
    
    # 文件属性
    file_size = db.Column(db.BigInteger, nullable=False, comment='文件大小(字节)')
    file_type = db.Column(db.String(50), comment='文件类型')
    mime_type = db.Column(db.String(100), comment='MIME类型')
    file_extension = db.Column(db.String(20), comment='文件扩展名')
    
    # 图像属性（如果是图像文件）
    image_width = db.Column(db.Integer, comment='图像宽度')
    image_height = db.Column(db.Integer, comment='图像高度')
    image_format = db.Column(db.String(20), comment='图像格式')
    has_thumbnail = db.Column(db.Boolean, default=False, comment='是否有缩略图')
    
    # 索引和搜索
    is_indexed = db.Column(db.Boolean, default=False, comment='是否已索引')
    search_keywords = db.Column(db.Text, comment='搜索关键词')
    image_features = db.Column(db.Text, comment='图像特征向量')
    
    # 状态信息
    is_available = db.Column(db.Boolean, default=True, comment='文件是否可用')
    is_deleted = db.Column(db.Boolean, default=False, comment='是否已删除')
    
    # 统计信息
    view_count = db.Column(db.Integer, default=0, comment='查看次数')
    download_count = db.Column(db.Integer, default=0, comment='下载次数')
    
    # 时间信息
    file_created_at = db.Column(db.DateTime, comment='文件创建时间')
    file_modified_at = db.Column(db.DateTime, comment='文件修改时间')
    indexed_at = db.Column(db.DateTime, comment='索引时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    shares = db.relationship('FileShare', backref='file', lazy='dynamic', cascade='all, delete-orphan')
    permissions = db.relationship('FilePermission', backref='file', lazy='dynamic', cascade='all, delete-orphan')
    
    @staticmethod
    def calculate_hash(file_path):
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return None
    
    def get_thumbnail_path(self, size='medium'):
        """获取缩略图路径"""
        if not self.has_thumbnail:
            return None
        
        from flask import current_app
        thumbnail_dir = current_app.config['THUMBNAIL_FOLDER']
        return os.path.join(thumbnail_dir, size, f"{self.file_hash}.jpg")
    
    def is_image(self):
        """判断是否为图像文件"""
        from flask import current_app
        supported_formats = current_app.config.get('SUPPORTED_IMAGE_FORMATS', set())
        return self.file_extension.lower() in supported_formats
    
    def is_video(self):
        """判断是否为视频文件"""
        from flask import current_app
        supported_formats = current_app.config.get('SUPPORTED_VIDEO_FORMATS', set())
        return self.file_extension.lower() in supported_formats
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'filename': self.filename,
            'relative_path': self.relative_path,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'mime_type': self.mime_type,
            'file_extension': self.file_extension,
            'image_width': self.image_width,
            'image_height': self.image_height,
            'image_format': self.image_format,
            'has_thumbnail': self.has_thumbnail,
            'is_available': self.is_available,
            'view_count': self.view_count,
            'download_count': self.download_count,
            'file_created_at': self.file_created_at.isoformat() if self.file_created_at else None,
            'file_modified_at': self.file_modified_at.isoformat() if self.file_modified_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<FileInfo {self.filename}>'

class FileShare(db.Model):
    """文件共享配置模型"""
    __tablename__ = 'file_shares'
    
    id = db.Column(db.Integer, primary_key=True)
    share_name = db.Column(db.String(100), unique=True, nullable=False, comment='共享名称')
    share_path = db.Column(db.Text, nullable=False, comment='共享路径')
    description = db.Column(db.Text, comment='共享描述')
    
    # 访问控制
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    internal_access = db.Column(db.Boolean, default=True, comment='内网访问')
    external_access = db.Column(db.Boolean, default=False, comment='外网访问')
    require_auth = db.Column(db.Boolean, default=True, comment='需要认证')
    
    # 权限设置
    allow_read = db.Column(db.Boolean, default=True, comment='允许读取')
    allow_write = db.Column(db.Boolean, default=False, comment='允许写入')
    allow_delete = db.Column(db.Boolean, default=False, comment='允许删除')
    allow_download = db.Column(db.Boolean, default=True, comment='允许下载')
    allow_upload = db.Column(db.Boolean, default=False, comment='允许上传')
    
    # 下载限制
    max_download_size = db.Column(db.BigInteger, comment='最大下载大小')
    max_download_count = db.Column(db.Integer, comment='最大下载数量')
    enable_encryption = db.Column(db.Boolean, default=False, comment='启用加密下载')
    encryption_threshold = db.Column(db.Integer, default=3, comment='加密阈值')
    
    # 监控设置
    enable_monitoring = db.Column(db.Boolean, default=True, comment='启用监控')
    sensitive_keywords = db.Column(db.Text, comment='敏感关键词')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 外键
    file_id = db.Column(db.Integer, db.ForeignKey('file_info.id'))
    
    def __repr__(self):
        return f'<FileShare {self.share_name}>'

class FilePermission(db.Model):
    """文件权限模型"""
    __tablename__ = 'file_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 外键
    file_id = db.Column(db.Integer, db.ForeignKey('file_info.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    group_id = db.Column(db.Integer, db.ForeignKey('user_groups.id'), nullable=True)
    
    # 权限设置
    can_read = db.Column(db.Boolean, default=True, comment='读取权限')
    can_write = db.Column(db.Boolean, default=False, comment='写入权限')
    can_delete = db.Column(db.Boolean, default=False, comment='删除权限')
    can_download = db.Column(db.Boolean, default=True, comment='下载权限')
    can_share = db.Column(db.Boolean, default=False, comment='分享权限')
    
    # 限制设置
    download_limit = db.Column(db.Integer, comment='下载次数限制')
    size_limit = db.Column(db.BigInteger, comment='大小限制')
    
    # 时间限制
    valid_from = db.Column(db.DateTime, comment='生效时间')
    valid_until = db.Column(db.DateTime, comment='失效时间')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = db.relationship('User', backref='file_permissions')
    group = db.relationship('UserGroup', backref='file_permissions')
    
    def is_valid(self):
        """检查权限是否有效"""
        now = datetime.utcnow()
        if self.valid_from and now < self.valid_from:
            return False
        if self.valid_until and now > self.valid_until:
            return False
        return True
    
    def __repr__(self):
        target = f"User:{self.user.username}" if self.user else f"Group:{self.group.name}"
        return f'<FilePermission {target} -> {self.file.filename}>'
